# Application name and server configuration
spring.application.name=bms-be
server.port=8080

# PostgreSQL Database configuration
spring.datasource.url=***************************************
spring.datasource.username=dbuser
spring.datasource.password=dbheslo
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate properties
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect

# Flyway migration
spring.flyway.enabled=true
spring.flyway.baseline-on-migrate=true
spring.flyway.locations=classpath:db/migration

# Connection pool settings
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.maximum-pool-size=5

# Error handling for database connection
spring.datasource.hikari.initialization-fail-timeout=-1
spring.datasource.continue-on-error=true

# Logging
logging.level.sk.umb.prog3.bms=INFO
