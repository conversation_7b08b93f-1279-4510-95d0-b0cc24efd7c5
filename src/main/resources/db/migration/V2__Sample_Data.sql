-- Insert sample users (passwords are all 'password123')
INSERT INTO users (id, username, email, password_hash, role, provider_name, created_at, is_active) VALUES
('550e8400-e29b-41d4-a716-446655440000', 'admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lQOjkNnL.Ga', 'admin', NULL, CURRENT_TIMESTAMP, true),
('550e8400-e29b-41d4-a716-446655440001', 'provider', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lQOjkNnL.Ga', 'provider', 'Slovak Telekom', CURRENT_TIMESTAMP, true),
('550e8400-e29b-41d4-a716-446655440002', 'provider2', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lQOjkNnL.Ga', 'provider', 'Orange Slovakia', CURRENT_TIMESTAMP, true),
('550e8400-e29b-41d4-a716-446655440003', 'viewer', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lQOjkNnL.Ga', 'viewer', NULL, CURRENT_TIMESTAMP, true);

-- Insert sample connection points (matching frontend mock data)
INSERT INTO connection_points (id, name, provider_id, provider_name, type, capacity, address, status, installation_date, latitude, longitude, properties) VALUES
-- Slovak Telekom points
('point1', 'Bratislava Central Hub', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'junction', 10000, 'Bratislava, Slovakia', 'active', '2020-01-01', 48.148, 17.107, '{}'),
('point2', 'Trnava Distribution', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'distribution', 5000, 'Trnava, Slovakia', 'active', '2020-02-01', 48.377, 17.583, '{}'),
('point3', 'Nitra Junction', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'junction', 8000, 'Nitra, Slovakia', 'active', '2020-03-01', 48.308, 18.087, '{}'),
('point4', 'Trenčín Endpoint', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'endpoint', 3000, 'Trenčín, Slovakia', 'active', '2020-04-01', 48.894, 18.044, '{}'),
('point5', 'Žilina Hub', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'junction', 7000, 'Žilina, Slovakia', 'active', '2020-05-01', 49.223, 18.740, '{}'),
('point6', 'Košice Central', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'junction', 9000, 'Košice, Slovakia', 'active', '2020-06-01', 48.717, 21.261, '{}'),
('point7', 'Prešov Distribution', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'distribution', 4000, 'Prešov, Slovakia', 'active', '2020-07-01', 49.002, 21.239, '{}'),
('point8', 'Banská Bystrica Hub', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'junction', 6000, 'Banská Bystrica, Slovakia', 'active', '2020-08-01', 48.736, 19.146, '{}'),
-- Orange Slovakia points
('point9', 'Orange Bratislava', '550e8400-e29b-41d4-a716-446655440002', 'Orange Slovakia', 'junction', 8000, 'Bratislava, Slovakia', 'active', '2021-01-01', 48.158, 17.117, '{}'),
('point10', 'Orange Košice', '550e8400-e29b-41d4-a716-446655440002', 'Orange Slovakia', 'junction', 7000, 'Košice, Slovakia', 'active', '2021-02-01', 48.727, 21.271, '{}'),
('point11', 'Orange Žilina', '550e8400-e29b-41d4-a716-446655440002', 'Orange Slovakia', 'distribution', 5000, 'Žilina, Slovakia', 'active', '2021-03-01', 49.233, 18.750, '{}'),
('point12', 'Orange Nitra', '550e8400-e29b-41d4-a716-446655440002', 'Orange Slovakia', 'endpoint', 3000, 'Nitra, Slovakia', 'planned', '2024-01-01', 48.318, 18.097, '{}');

-- Insert sample optical lines (matching frontend mock data)
INSERT INTO optical_lines (id, name, provider_id, provider_name, start_point_id, end_point_id, capacity, used_capacity, length, status, installation_date, geometry_coordinates, properties) VALUES
-- Slovak Telekom lines
('1', 'Bratislava-Trnava', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'point1', 'point2', 1000, 750, 47.5, 'active', '2020-01-15', '[[17.107, 48.148], [17.583, 48.377]]', '{}'),
('2', 'Trnava-Nitra', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'point2', 'point3', 800, 600, 65.2, 'active', '2020-02-20', '[[17.583, 48.377], [18.087, 48.308]]', '{}'),
('3', 'Bratislava-Trenčín', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'point1', 'point4', 600, 450, 120.8, 'active', '2020-03-10', '[[17.107, 48.148], [18.044, 48.894]]', '{}'),
('4', 'Trenčín-Žilina', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'point4', 'point5', 500, 300, 85.3, 'active', '2020-04-05', '[[18.044, 48.894], [18.740, 49.223]]', '{}'),
('5', 'Žilina-Košice', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'point5', 'point6', 1200, 900, 280.5, 'active', '2020-05-12', '[[18.740, 49.223], [21.261, 48.717]]', '{}'),
('6', 'Košice-Prešov', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'point6', 'point7', 400, 250, 35.7, 'active', '2020-06-18', '[[21.261, 48.717], [21.239, 49.002]]', '{}'),
('7', 'Nitra-Banská Bystrica', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'point3', 'point8', 700, 500, 95.4, 'active', '2020-07-22', '[[18.087, 48.308], [19.146, 48.736]]', '{}'),
('8', 'Bratislava-Banská Bystrica', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'point1', 'point8', 900, 650, 180.2, 'maintenance', '2020-08-30', '[[17.107, 48.148], [19.146, 48.736]]', '{}'),
-- Orange Slovakia lines
('9', 'Orange Bratislava-Košice', '550e8400-e29b-41d4-a716-446655440002', 'Orange Slovakia', 'point9', 'point10', 1500, 1100, 350.8, 'active', '2021-01-20', '[[17.117, 48.158], [21.271, 48.727]]', '{}'),
('10', 'Orange Košice-Žilina', '550e8400-e29b-41d4-a716-446655440002', 'Orange Slovakia', 'point10', 'point11', 800, 600, 220.3, 'active', '2021-02-25', '[[21.271, 48.727], [18.750, 49.233]]', '{}'),
('11', 'Orange Žilina-Nitra', '550e8400-e29b-41d4-a716-446655440002', 'Orange Slovakia', 'point11', 'point12', 600, 0, 140.7, 'planned', '2024-03-01', '[[18.750, 49.233], [18.097, 48.318]]', '{}');
