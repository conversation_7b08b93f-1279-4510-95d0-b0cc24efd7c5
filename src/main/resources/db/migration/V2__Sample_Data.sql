-- Insert sample providers
INSERT INTO providers (id, name, contact_email, contact_phone, active)
VALUES 
  ('11111111-1111-1111-1111-111111111111', 'Telekom', '<EMAIL>', '+421900123456', true),
  ('22222222-2222-2222-2222-222222222222', 'Orange', '<EMAIL>', '+421901234567', true),
  ('33333333-3333-3333-3333-333333333333', 'O2', '<EMAIL>', '+421902345678', true);

-- Insert sample connection points
INSERT INTO connection_points (id, name, provider_id, type, capacity, address, status, latitude, longitude)
VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Junction A', '11111111-1111-1111-1111-111111111111', 'junction', 100, 'Main Street 1, Bratislava', 'active', 48.1486, 17.1077),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Endpoint B', '22222222-2222-2222-2222-222222222222', 'endpoint', 50, 'Secondary Street 2, Ko<PERSON>ice', 'active', 48.7164, 21.2611),
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'Distribution C', '33333333-3333-3333-3333-333333333333', 'distribution', 200, 'Third Street 3, Banská Bystrica', 'active', 48.7395, 19.1535);

-- Insert sample optical lines
INSERT INTO optical_lines (id, name, provider_id, capacity, utilization, status, path_coordinates)
VALUES
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', 'Line 1', '11111111-1111-1111-1111-111111111111', 100, 30, 'active', '[{"lat": 48.1486, "lng": 17.1077}, {"lat": 48.7395, "lng": 19.1535}]'::jsonb),
  ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'Line 2', '22222222-2222-2222-2222-222222222222', 50, 20, 'active', '[{"lat": 48.7395, "lng": 19.1535}, {"lat": 48.7164, "lng": 21.2611}]'::jsonb);

-- Connect connection points to optical lines
INSERT INTO connection_point_line (connection_point_id, optical_line_id, is_source)
VALUES 
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'dddddddd-dddd-dddd-dddd-dddddddddddd', true),
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'dddddddd-dddd-dddd-dddd-dddddddddddd', false),
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', true),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', false);
