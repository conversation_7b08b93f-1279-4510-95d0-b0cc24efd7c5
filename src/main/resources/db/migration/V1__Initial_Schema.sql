-- PostGIS extension removed - using simple coordinates instead

-- <PERSON><PERSON> enum types
CREATE TYPE connection_point_type AS ENUM ('junction', 'endpoint', 'distribution');
CREATE TYPE connection_point_status AS ENUM ('active', 'planned', 'maintenance', 'inactive');
CREATE TYPE optical_line_status AS ENUM ('active', 'planned', 'maintenance', 'inactive');
CREATE TYPE user_role AS ENUM ('admin', 'provider', 'viewer');

-- Create providers table
CREATE TABLE providers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role user_role NOT NULL,
    provider_id UUID REFERENCES providers(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    active BOOLEAN DEFAULT TRUE
);

-- Create connection_points table
CREATE TABLE connection_points (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    provider_id UUID REFERENCES providers(id) ON DELETE CASCADE,
    type connection_point_type NOT NULL,
    capacity INTEGER NOT NULL DEFAULT 0,
    address VARCHAR(255),
    status connection_point_status NOT NULL DEFAULT 'planned',
    installation_date TIMESTAMP,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    properties JSONB
);

-- Create optical_lines table
CREATE TABLE optical_lines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    provider_id UUID REFERENCES providers(id) ON DELETE CASCADE,
    capacity INTEGER NOT NULL DEFAULT 0,
    utilization INTEGER NOT NULL DEFAULT 0,
    status optical_line_status NOT NULL DEFAULT 'planned',
    installation_date TIMESTAMP,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    path_coordinates JSONB, -- Array of coordinate pairs: [{"lat": 48.1486, "lng": 17.1077}, ...]
    properties JSONB
);

-- Create connection_point_line table for many-to-many relationship
CREATE TABLE connection_point_line (
    connection_point_id UUID REFERENCES connection_points(id) ON DELETE CASCADE,
    optical_line_id UUID REFERENCES optical_lines(id) ON DELETE CASCADE,
    is_source BOOLEAN NOT NULL DEFAULT FALSE,
    PRIMARY KEY (connection_point_id, optical_line_id)
);

-- Create refresh_tokens table
CREATE TABLE refresh_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(255) NOT NULL UNIQUE,
    expiry_date TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_connection_points_provider ON connection_points(provider_id);
CREATE INDEX idx_optical_lines_provider ON optical_lines(provider_id);
CREATE INDEX idx_users_provider ON users(provider_id);
CREATE INDEX idx_connection_points_coordinates ON connection_points(latitude, longitude);
CREATE INDEX idx_optical_lines_path_coordinates ON optical_lines USING GIN(path_coordinates);

-- Create triggers for last_modified
CREATE OR REPLACE FUNCTION update_last_modified()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_modified = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_connection_points_last_modified
BEFORE UPDATE ON connection_points
FOR EACH ROW EXECUTE FUNCTION update_last_modified();

CREATE TRIGGER update_optical_lines_last_modified
BEFORE UPDATE ON optical_lines
FOR EACH ROW EXECUTE FUNCTION update_last_modified();

CREATE TRIGGER update_providers_last_modified
BEFORE UPDATE ON providers
FOR EACH ROW EXECUTE FUNCTION update_last_modified();

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, password_hash, role)
VALUES ('admin', '<EMAIL>', '$2a$10$dXJ3SW6G7P50lGmMkkmwe.20cQQubK3.HZWzG3YB1tlRy.fqvM/BG', 'admin');
