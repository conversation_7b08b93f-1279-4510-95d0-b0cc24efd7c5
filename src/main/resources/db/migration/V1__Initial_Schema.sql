-- Create users table (matching requirements exactly)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'provider', 'viewer')),
    provider_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Create connection_points table (matching requirements exactly)
CREATE TABLE connection_points (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    provider_id UUID NOT NULL REFERENCES users(id),
    provider_name VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('junction', 'endpoint', 'distribution')),
    capacity INTEGER NOT NULL,
    address TEXT,
    status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'planned', 'maintenance', 'inactive')),
    installation_date DATE,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    properties JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create optical_lines table (matching requirements exactly)
CREATE TABLE optical_lines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    provider_id UUID NOT NULL REFERENCES users(id),
    provider_name VARCHAR(100) NOT NULL,
    start_point_id UUID NOT NULL REFERENCES connection_points(id),
    end_point_id UUID NOT NULL REFERENCES connection_points(id),
    capacity INTEGER NOT NULL,
    used_capacity INTEGER DEFAULT 0,
    length DECIMAL(10, 2) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'planned', 'maintenance', 'inactive')),
    installation_date DATE,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    geometry_coordinates TEXT NOT NULL, -- JSON array of [lng, lat] coordinates
    properties JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes (matching requirements)
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_connection_points_provider ON connection_points(provider_id);
CREATE INDEX idx_connection_points_status ON connection_points(status);
CREATE INDEX idx_connection_points_location ON connection_points(latitude, longitude);
CREATE INDEX idx_optical_lines_provider ON optical_lines(provider_id);
CREATE INDEX idx_optical_lines_status ON optical_lines(status);
CREATE INDEX idx_optical_lines_points ON optical_lines(start_point_id, end_point_id);
