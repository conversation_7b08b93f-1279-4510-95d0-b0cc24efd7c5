package sk.umb.prog3.bms.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sk.umb.prog3.bms.dto.ConnectionPointDto;
import sk.umb.prog3.bms.exception.BadRequestException;
import sk.umb.prog3.bms.model.ConnectionPoint;
import sk.umb.prog3.bms.repository.ConnectionPointRepository;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Transactional
public class ConnectionPointService {

    @Autowired
    private ConnectionPointRepository connectionPointRepository;

    @Transactional(readOnly = true)
    public List<ConnectionPointDto> getAllPoints(UUID providerId) {
        List<ConnectionPoint> points = providerId != null
            ? connectionPointRepository.findByProviderId(providerId)
            : connectionPointRepository.findAll();
        return points.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public ConnectionPointDto getPointById(UUID id) {
        ConnectionPoint point = connectionPointRepository.findById(id)
            .orElseThrow(() -> new BadRequestException("Connection point not found with id: " + id));
        return convertToDto(point);
    }

    public void deletePoint(UUID id) {
        connectionPointRepository.deleteById(id);
    }

    private ConnectionPointDto convertToDto(ConnectionPoint entity) {
        ConnectionPointDto dto = new ConnectionPointDto();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setProviderId(entity.getProviderId());
        dto.setProviderName(entity.getProviderName());
        dto.setType(entity.getType());
        dto.setCapacity(entity.getCapacity());
        dto.setAddress(entity.getAddress());
        dto.setStatus(entity.getStatus());
        dto.setInstallationDate(entity.getInstallationDate());
        dto.setLastModified(entity.getLastModified());
        dto.setLatitude(entity.getLatitude());
        dto.setLongitude(entity.getLongitude());
        dto.setProperties(entity.getProperties());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        return dto;
    }
}
