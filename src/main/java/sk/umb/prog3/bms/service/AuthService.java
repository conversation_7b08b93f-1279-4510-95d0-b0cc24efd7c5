package sk.umb.prog3.bms.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import sk.umb.prog3.bms.dto.JwtResponse;
import sk.umb.prog3.bms.dto.LoginRequest;
import sk.umb.prog3.bms.dto.RegisterRequest;
import sk.umb.prog3.bms.exception.BadRequestException;
import sk.umb.prog3.bms.model.User;
import sk.umb.prog3.bms.model.UserRole;
import sk.umb.prog3.bms.repository.UserRepository;
import sk.umb.prog3.bms.security.JwtUtils;
import sk.umb.prog3.bms.security.UserPrincipal;

@Service
public class AuthService {

    @Autowired
    AuthenticationManager authenticationManager;

    @Autowired
    UserRepository userRepository;

    @Autowired
    PasswordEncoder encoder;

    @Autowired
    JwtUtils jwtUtils;

    public JwtResponse authenticateUser(LoginRequest loginRequest) {
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword()));

        SecurityContextHolder.getContext().setAuthentication(authentication);
        String jwt = jwtUtils.generateJwtToken(authentication);

        UserPrincipal userDetails = (UserPrincipal) authentication.getPrincipal();
        User user = userRepository.findByUsername(userDetails.getUsername())
                .orElseThrow(() -> new RuntimeException("User not found"));

        return new JwtResponse(jwt,
                userDetails.getId(),
                userDetails.getUsername(),
                userDetails.getEmail(),
                user.getRole(),
                user.getProviderName());
    }

    public void registerUser(RegisterRequest signUpRequest) {
        if (userRepository.existsByUsername(signUpRequest.getUsername())) {
            throw new BadRequestException("Error: Username is already taken!");
        }

        if (userRepository.existsByEmail(signUpRequest.getEmail())) {
            throw new BadRequestException("Error: Email is already in use!");
        }

        // Validate provider name for provider role
        if (signUpRequest.getRole() == UserRole.provider && 
            (signUpRequest.getProviderName() == null || signUpRequest.getProviderName().trim().isEmpty())) {
            throw new BadRequestException("Provider name is required for provider role");
        }

        // Create new user's account
        User user = new User(signUpRequest.getUsername(),
                           signUpRequest.getEmail(),
                           encoder.encode(signUpRequest.getPassword()),
                           signUpRequest.getRole());

        if (signUpRequest.getRole() == UserRole.provider) {
            user.setProviderName(signUpRequest.getProviderName());
        }

        userRepository.save(user);
    }
}
