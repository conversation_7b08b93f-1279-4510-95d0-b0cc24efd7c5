package sk.umb.prog3.bms.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sk.umb.prog3.bms.dto.CreateOpticalLineDto;
import sk.umb.prog3.bms.dto.OpticalLineDto;
import sk.umb.prog3.bms.exception.BadRequestException;
import sk.umb.prog3.bms.model.ConnectionPoint;
import sk.umb.prog3.bms.model.OpticalLine;
import sk.umb.prog3.bms.repository.ConnectionPointRepository;
import sk.umb.prog3.bms.repository.OpticalLineRepository;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Transactional
public class OpticalLineService {

    @Autowired
    private OpticalLineRepository opticalLineRepository;

    @Autowired
    private ConnectionPointRepository connectionPointRepository;

    @Transactional(readOnly = true)
    public List<OpticalLineDto> getAllLines(UUID providerId) {
        List<OpticalLine> lines = providerId != null
            ? opticalLineRepository.findByProviderId(providerId)
            : opticalLineRepository.findAll();
        return lines.stream().map(this::convertToDto).collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public OpticalLineDto getLineById(UUID id) {
        OpticalLine line = opticalLineRepository.findById(id)
            .orElseThrow(() -> new BadRequestException("Optical line not found with id: " + id));
        return convertToDto(line);
    }

    public OpticalLineDto createLine(CreateOpticalLineDto createDto, UUID currentUserId, String providerName) {
        // Validate that start and end points exist and belong to the same provider
        ConnectionPoint startPoint = connectionPointRepository.findById(createDto.getStartPointId())
            .orElseThrow(() -> new BadRequestException("Start point not found"));
        ConnectionPoint endPoint = connectionPointRepository.findById(createDto.getEndPointId())
            .orElseThrow(() -> new BadRequestException("End point not found"));

        if (!startPoint.getProviderId().equals(currentUserId) ||
            !endPoint.getProviderId().equals(currentUserId)) {
            throw new BadRequestException("Cannot create line between points of different providers");
        }

        OpticalLine line = new OpticalLine();
        line.setName(createDto.getName());
        line.setProviderId(currentUserId);
        line.setProviderName(providerName);
        line.setStartPointId(createDto.getStartPointId());
        line.setEndPointId(createDto.getEndPointId());
        line.setCapacity(createDto.getCapacity());
        line.setUsedCapacity(createDto.getUsedCapacity());
        line.setStatus(createDto.getStatus());
        line.setInstallationDate(createDto.getInstallationDate());
        line.setGeometryCoordinates(createDto.getGeometryCoordinates());
        line.setProperties(createDto.getProperties());

        // Calculate line length based on coordinates (simplified)
        double calculatedLength = calculateLineLength(createDto.getGeometryCoordinates());
        line.setLength(BigDecimal.valueOf(calculatedLength));

        OpticalLine savedLine = opticalLineRepository.save(line);
        return convertToDto(savedLine);
    }

    public void deleteLine(UUID id) {
        opticalLineRepository.deleteById(id);
    }
    
    private OpticalLineDto convertToDto(OpticalLine entity) {
        OpticalLineDto dto = new OpticalLineDto();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setProviderId(entity.getProviderId());
        dto.setProviderName(entity.getProviderName());
        dto.setStartPointId(entity.getStartPointId());
        dto.setEndPointId(entity.getEndPointId());
        dto.setCapacity(entity.getCapacity());
        dto.setUsedCapacity(entity.getUsedCapacity());
        dto.setLength(entity.getLength());
        dto.setStatus(entity.getStatus());
        dto.setInstallationDate(entity.getInstallationDate());
        dto.setLastModified(entity.getLastModified());
        dto.setGeometryCoordinates(entity.getGeometryCoordinates());
        dto.setProperties(entity.getProperties());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        return dto;
    }

    private double calculateLineLength(String geometryCoordinates) {
        // Implementation of Haversine formula for distance calculation
        // Parse JSON coordinates and calculate total distance
        // This is a simplified version - implement full calculation
        return 100.0; // Placeholder
    }
}
