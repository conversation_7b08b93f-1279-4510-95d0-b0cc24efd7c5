package sk.umb.prog3.bms.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import sk.umb.prog3.bms.model.OpticalLine;
import sk.umb.prog3.bms.model.Status;

import java.util.List;
import java.util.UUID;

@Repository
public interface OpticalLineRepository extends JpaRepository<OpticalLine, UUID> {
    List<OpticalLine> findByProviderId(UUID providerId);
    List<OpticalLine> findByStatus(Status status);

    @Query("SELECT ol FROM OpticalLine ol WHERE ol.providerId = :providerId AND ol.status = :status")
    List<OpticalLine> findByProviderIdAndStatus(@Param("providerId") UUID providerId, @Param("status") Status status);
}
