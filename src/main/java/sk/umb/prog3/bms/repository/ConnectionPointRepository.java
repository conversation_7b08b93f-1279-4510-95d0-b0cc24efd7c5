package sk.umb.prog3.bms.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import sk.umb.prog3.bms.model.ConnectionPoint;
import sk.umb.prog3.bms.model.PointType;
import sk.umb.prog3.bms.model.Status;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@Repository
public interface ConnectionPointRepository extends JpaRepository<ConnectionPoint, UUID> {
    List<ConnectionPoint> findByProviderId(UUID providerId);
    List<ConnectionPoint> findByType(PointType type);
    List<ConnectionPoint> findByStatus(Status status);

    @Query("SELECT cp FROM ConnectionPoint cp WHERE " +
           "cp.latitude BETWEEN :minLat AND :maxLat AND " +
           "cp.longitude BETWEEN :minLng AND :maxLng")
    List<ConnectionPoint> findPointsInBoundingBox(@Param("minLat") BigDecimal minLat,
                                                @Param("maxLat") BigDecimal maxLat,
                                                @Param("minLng") BigDecimal minLng,
                                                @Param("maxLng") BigDecimal maxLng);
}
