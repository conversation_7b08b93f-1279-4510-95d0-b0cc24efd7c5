package sk.umb.prog3.bms.persistence;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface OpticalLineRepository extends JpaRepository<OpticalLineEntity, UUID> {

    List<OpticalLineEntity> findByProviderId(UUID providerId);

    List<OpticalLineEntity> findByStatus(String status);

    List<OpticalLineEntity> findByCapacityGreaterThan(Integer capacity);
}
