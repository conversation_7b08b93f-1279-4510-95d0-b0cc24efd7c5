package sk.umb.prog3.bms.dto;

import sk.umb.prog3.bms.model.Status;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

public class OpticalLineDto {
    private UUID id;
    private String name;
    private UUID providerId;
    private String providerName;
    private UUID startPointId;
    private UUID endPointId;
    private Integer capacity;
    private Integer usedCapacity;
    private BigDecimal length;
    private Status status;
    private LocalDate installationDate;
    private LocalDateTime lastModified;
    private String geometryCoordinates; // JSON string for coordinates
    private Map<String, Object> properties;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Default constructor
    public OpticalLineDto() {}

    // Getters and setters
    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public UUID getProviderId() {
        return providerId;
    }

    public void setProviderId(UUID providerId) {
        this.providerId = providerId;
    }

    public String getProviderName() {
        return providerName;
    }

    public void setProviderName(String providerName) {
        this.providerName = providerName;
    }

    public UUID getStartPointId() {
        return startPointId;
    }

    public void setStartPointId(UUID startPointId) {
        this.startPointId = startPointId;
    }

    public UUID getEndPointId() {
        return endPointId;
    }

    public void setEndPointId(UUID endPointId) {
        this.endPointId = endPointId;
    }

    public Integer getCapacity() {
        return capacity;
    }

    public void setCapacity(Integer capacity) {
        this.capacity = capacity;
    }

    public Integer getUsedCapacity() {
        return usedCapacity;
    }

    public void setUsedCapacity(Integer usedCapacity) {
        this.usedCapacity = usedCapacity;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public LocalDate getInstallationDate() {
        return installationDate;
    }

    public void setInstallationDate(LocalDate installationDate) {
        this.installationDate = installationDate;
    }

    public LocalDateTime getLastModified() {
        return lastModified;
    }

    public void setLastModified(LocalDateTime lastModified) {
        this.lastModified = lastModified;
    }

    public String getGeometryCoordinates() {
        return geometryCoordinates;
    }

    public void setGeometryCoordinates(String geometryCoordinates) {
        this.geometryCoordinates = geometryCoordinates;
    }

    public Map<String, Object> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}
