package sk.umb.prog3.bms.dto;

import jakarta.validation.constraints.*;
import sk.umb.prog3.bms.model.Status;

import java.time.LocalDate;
import java.util.Map;
import java.util.UUID;

public class CreateOpticalLineDto {
    @NotBlank(message = "Name is required")
    @Size(max = 100, message = "Name must not exceed 100 characters")
    private String name;

    @NotNull(message = "Start point ID is required")
    private UUID startPointId;

    @NotNull(message = "End point ID is required")
    private UUID endPointId;

    @NotNull(message = "Capacity is required")
    @Positive(message = "Capacity must be positive")
    private Integer capacity;

    @Min(value = 0, message = "Used capacity cannot be negative")
    private Integer usedCapacity = 0;

    @NotNull(message = "Status is required")
    private Status status;

    private LocalDate installationDate;

    @NotBlank(message = "Geometry coordinates are required")
    private String geometryCoordinates;

    private Map<String, Object> properties;

    // Default constructor
    public CreateOpticalLineDto() {}

    // Getters and setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public UUID getStartPointId() {
        return startPointId;
    }

    public void setStartPointId(UUID startPointId) {
        this.startPointId = startPointId;
    }

    public UUID getEndPointId() {
        return endPointId;
    }

    public void setEndPointId(UUID endPointId) {
        this.endPointId = endPointId;
    }

    public Integer getCapacity() {
        return capacity;
    }

    public void setCapacity(Integer capacity) {
        this.capacity = capacity;
    }

    public Integer getUsedCapacity() {
        return usedCapacity;
    }

    public void setUsedCapacity(Integer usedCapacity) {
        this.usedCapacity = usedCapacity;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public LocalDate getInstallationDate() {
        return installationDate;
    }

    public void setInstallationDate(LocalDate installationDate) {
        this.installationDate = installationDate;
    }

    public String getGeometryCoordinates() {
        return geometryCoordinates;
    }

    public void setGeometryCoordinates(String geometryCoordinates) {
        this.geometryCoordinates = geometryCoordinates;
    }

    public Map<String, Object> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }
}
