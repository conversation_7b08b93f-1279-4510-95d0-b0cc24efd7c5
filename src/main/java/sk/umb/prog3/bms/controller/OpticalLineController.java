package sk.umb.prog3.bms.controller;

import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import sk.umb.prog3.bms.dto.CreateOpticalLineDto;
import sk.umb.prog3.bms.dto.OpticalLineDto;
import sk.umb.prog3.bms.model.User;
import sk.umb.prog3.bms.model.UserRole;
import sk.umb.prog3.bms.repository.UserRepository;
import sk.umb.prog3.bms.security.UserPrincipal;
import sk.umb.prog3.bms.service.OpticalLineService;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/lines")
public class OpticalLineController {

    @Autowired
    private OpticalLineService opticalLineService;

    @Autowired
    private UserRepository userRepository;

    @GetMapping
    public ResponseEntity<List<OpticalLineDto>> getAllLines(
            @RequestParam(required = false) UUID providerId,
            Authentication authentication) {

        // If user is a provider, filter by their ID
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        User currentUser = userRepository.findByUsername(userPrincipal.getUsername())
                .orElseThrow(() -> new RuntimeException("User not found"));

        if (currentUser.getRole() == UserRole.provider) {
            providerId = currentUser.getId();
        }

        List<OpticalLineDto> lines = opticalLineService.getAllLines(providerId);
        return ResponseEntity.ok(lines);
    }

    @GetMapping("/{id}")
    public ResponseEntity<OpticalLineDto> getLineById(@PathVariable UUID id) {
        OpticalLineDto line = opticalLineService.getLineById(id);
        return ResponseEntity.ok(line);
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('PROVIDER')")
    public ResponseEntity<OpticalLineDto> createLine(
            @Valid @RequestBody CreateOpticalLineDto createDto,
            Authentication authentication) {

        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        User currentUser = userRepository.findByUsername(userPrincipal.getUsername())
                .orElseThrow(() -> new RuntimeException("User not found"));

        OpticalLineDto createdLine = opticalLineService.createLine(createDto, currentUser.getId(), currentUser.getProviderName());
        return ResponseEntity.status(HttpStatus.CREATED).body(createdLine);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteLine(@PathVariable UUID id) {
        opticalLineService.deleteLine(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/test")
    public String test() {
        return "BMS-BE API is running!";
    }
}
