package sk.umb.prog3.bms.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import sk.umb.prog3.bms.dto.ConnectionPointDto;
import sk.umb.prog3.bms.model.User;
import sk.umb.prog3.bms.model.UserRole;
import sk.umb.prog3.bms.repository.UserRepository;
import sk.umb.prog3.bms.security.UserPrincipal;
import sk.umb.prog3.bms.service.ConnectionPointService;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/points")
public class ConnectionPointController {

    @Autowired
    private ConnectionPointService connectionPointService;

    @Autowired
    private UserRepository userRepository;

    @GetMapping
    public ResponseEntity<List<ConnectionPointDto>> getAllPoints(
            @RequestParam(required = false) UUID providerId,
            Authentication authentication) {

        // If user is a provider, filter by their ID
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        User currentUser = userRepository.findByUsername(userPrincipal.getUsername())
                .orElseThrow(() -> new RuntimeException("User not found"));

        if (currentUser.getRole() == UserRole.provider) {
            providerId = currentUser.getId();
        }

        List<ConnectionPointDto> points = connectionPointService.getAllPoints(providerId);
        return ResponseEntity.ok(points);
    }

    @GetMapping("/{id}")
    public ResponseEntity<ConnectionPointDto> getPointById(@PathVariable UUID id) {
        ConnectionPointDto point = connectionPointService.getPointById(id);
        return ResponseEntity.ok(point);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deletePoint(@PathVariable UUID id) {
        connectionPointService.deletePoint(id);
        return ResponseEntity.noContent().build();
    }
}
