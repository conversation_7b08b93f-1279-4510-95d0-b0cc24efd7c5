# PostgreSQL 17.4 Compatibility Changes

This document outlines the changes made to ensure compatibility with PostgreSQL 17.4 and remove PostGIS dependencies.

## Changes Made

### 1. Database Dependencies (pom.xml)
- **Updated PostgreSQL driver** to version 42.7.4 (compatible with PostgreSQL 17.4)
- **Removed PostGIS JDBC dependency** (`net.postgis:postgis-jdbc:2.5.0`)
- Kept Spring Boot 2.7.18 for stability

### 2. Database Configuration (application.properties)
- **Updated Hibernate dialect** from `PostgreSQL10Dialect` to `PostgreSQLDialect` (auto-detects version)
- This ensures compatibility with PostgreSQL 17.4 features

### 3. Database Schema Changes
All SQL files updated to use simple coordinates instead of PostGIS:

#### Connection Points Table
- **Before**: `location GEOMETRY(Point, 4326)`
- **After**: `latitude DECIMAL(10, 8)` and `longitude DECIMAL(11, 8)`

#### Optical Lines Table
- **Before**: `path GEOMETRY(<PERSON><PERSON>tring, 4326)`
- **After**: `path_coordinates JSONB` (stores coordinate arrays as JSON)

#### Indexes
- **Before**: `CREATE INDEX ... USING GIST(location)`
- **After**: `CREATE INDEX ... (latitude, longitude)` and `USING GIN(path_coordinates)`

### 4. Sample Data Format
- **Before**: `ST_SetSRID(ST_MakePoint(17.1077, 48.1486), 4326)`
- **After**: Simple values: `48.1486, 17.1077`
- **Before**: `ST_MakeLine(ST_MakePoint(...), ST_MakePoint(...))`
- **After**: JSON arrays: `'[{"lat": 48.1486, "lng": 17.1077}, {"lat": 48.7395, "lng": 19.1535}]'::jsonb`

### 5. Files Updated
- `pom.xml` - Updated PostgreSQL driver, removed PostGIS
- `src/main/resources/application.properties` - Updated Hibernate dialect
- `src/main/resources/db/migration/V1__Initial_Schema.sql` - Removed PostGIS, updated schema
- `src/main/resources/db/migration/V2__Sample_Data.sql` - Updated sample data format
- `init-db-simple.sql` - Already updated with simple coordinates
- `init-db.sql` - Removed PostGIS extension
- `init-db.sh` - Enhanced PostgreSQL 17.4 feature detection
- `src/main/java/sk/umb/prog3/bms/persistence/OpticalLineEntity.java` - Updated to use UUID and new schema
- `src/main/java/sk/umb/prog3/bms/persistence/OpticalLineRepository.java` - Updated for UUID primary keys

## PostgreSQL 17.4 Features Utilized
- **Enhanced JSONB support** for storing coordinate arrays
- **Improved UUID generation** with `gen_random_uuid()`
- **Better performance** with GIN indexes on JSONB columns

## Coordinate Storage Format

### Connection Points
```sql
latitude DECIMAL(10, 8),   -- e.g., 48.1486
longitude DECIMAL(11, 8)   -- e.g., 17.1077
```

### Optical Lines
```json
[
  {"lat": 48.1486, "lng": 17.1077},
  {"lat": 48.7395, "lng": 19.1535}
]
```

## Benefits
1. **No PostGIS dependency** - Works with standard PostgreSQL
2. **Better portability** - Easier to deploy and maintain
3. **PostgreSQL 17.4 optimized** - Uses latest features and performance improvements
4. **Simple coordinate handling** - Easier to work with in applications
5. **JSON flexibility** - Easy to extend coordinate data with additional properties

## Migration Notes
- Existing PostGIS data would need to be converted to the new format
- Applications using PostGIS functions need to be updated to work with simple coordinates
- The new format is more portable and doesn't require special PostgreSQL extensions

## Testing
After making these changes, test the application with:
1. Database initialization: `./init-db.sh`
2. Application startup: `./start.sh`
3. Verify coordinate data is properly stored and retrieved
