#!/bin/bash

echo "BMS-BE Startup Script"
echo "====================="

# Check Java version
if ! command -v java &> /dev/null; then
  echo "Error: Java is not installed or not in PATH"
  exit 1
fi

# Run database initialization script
echo "Initializing database..."
./init-db.sh

# Check if database initialization was successful
if [ $? -ne 0 ]; then
  echo "Database initialization failed. Please check the error messages above."
  exit 1
fi

echo ""
echo "Database initialization completed successfully!"
echo ""

# Build the application
echo "Building the application..."
./mvnw clean package -DskipTests

# Check if build was successful
if [ $? -ne 0 ]; then
  echo "Build failed. Please check the error messages above."
  exit 1
fi

# Run the application
echo "Starting the application..."
java -jar target/bms-be-0.0.1-SNAPSHOT.jar
