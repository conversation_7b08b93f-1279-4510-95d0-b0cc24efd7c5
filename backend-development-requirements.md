# Java Backend Development Requirements for Optical Lines Database

## Project Overview

This document outlines the requirements for developing a Java backend API to support the Optical Lines Database frontend application. The backend will manage optical fiber network infrastructure data, including optical lines, connection points, and user management with role-based access control.

## Technology Stack Requirements

### Core Framework
- **Java**: Version 17 or higher
- **Spring Boot**: Version 3.x
- **Spring Security**: For authentication and authorization
- **Spring Data JPA**: For database operations
- **Spring Web**: For REST API endpoints

### Database
- **PostgreSQL**: Primary database (avoid PostGIS extensions)
- **H2**: For testing and development
- **Flyway or Liquibase**: For database migrations

### Additional Dependencies
- **JWT**: For token-based authentication
- **Jackson**: For JSON serialization/deserialization
- **Validation API**: For request validation
- **MapStruct**: For entity-DTO mapping
- **Swagger/OpenAPI**: For API documentation

## Database Schema

### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'provider', 'viewer')),
    provider_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);
```

### Connection Points Table
```sql
CREATE TABLE connection_points (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    provider_id UUID NOT NULL REFERENCES users(id),
    provider_name VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('junction', 'endpoint', 'distribution')),
    capacity INTEGER NOT NULL,
    address TEXT,
    status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'planned', 'maintenance', 'inactive')),
    installation_date DATE,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    properties JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Optical Lines Table
```sql
CREATE TABLE optical_lines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    provider_id UUID NOT NULL REFERENCES users(id),
    provider_name VARCHAR(100) NOT NULL,
    start_point_id UUID NOT NULL REFERENCES connection_points(id),
    end_point_id UUID NOT NULL REFERENCES connection_points(id),
    capacity INTEGER NOT NULL,
    used_capacity INTEGER DEFAULT 0,
    length DECIMAL(10, 2) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'planned', 'maintenance', 'inactive')),
    installation_date DATE,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    geometry_coordinates TEXT NOT NULL, -- JSON array of [lng, lat] coordinates
    properties JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Indexes
```sql
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_connection_points_provider ON connection_points(provider_id);
CREATE INDEX idx_connection_points_status ON connection_points(status);
CREATE INDEX idx_connection_points_location ON connection_points(latitude, longitude);
CREATE INDEX idx_optical_lines_provider ON optical_lines(provider_id);
CREATE INDEX idx_optical_lines_status ON optical_lines(status);
CREATE INDEX idx_optical_lines_points ON optical_lines(start_point_id, end_point_id);
```

## Data Models (Java Entities)

### User Entity
```java
@Entity
@Table(name = "users")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @Column(unique = true, nullable = false, length = 50)
    private String username;
    
    @Column(unique = true, nullable = false, length = 100)
    private String email;
    
    @Column(name = "password_hash", nullable = false)
    private String passwordHash;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private UserRole role;
    
    @Column(name = "provider_name", length = 100)
    private String providerName;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "last_login")
    private LocalDateTime lastLogin;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
}

public enum UserRole {
    admin, provider, viewer
}
```

### ConnectionPoint Entity
```java
@Entity
@Table(name = "connection_points")
public class ConnectionPoint {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @Column(nullable = false, length = 100)
    private String name;
    
    @Column(name = "provider_id", nullable = false)
    private UUID providerId;
    
    @Column(name = "provider_name", nullable = false, length = 100)
    private String providerName;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PointType type;
    
    @Column(nullable = false)
    private Integer capacity;
    
    private String address;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Status status;
    
    @Column(name = "installation_date")
    private LocalDate installationDate;
    
    @UpdateTimestamp
    @Column(name = "last_modified")
    private LocalDateTime lastModified;
    
    @Column(nullable = false, precision = 10, scale = 8)
    private BigDecimal latitude;
    
    @Column(nullable = false, precision = 11, scale = 8)
    private BigDecimal longitude;
    
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> properties;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}

public enum PointType {
    junction, endpoint, distribution
}

public enum Status {
    active, planned, maintenance, inactive
}
```

### OpticalLine Entity
```java
@Entity
@Table(name = "optical_lines")
public class OpticalLine {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @Column(nullable = false, length = 100)
    private String name;
    
    @Column(name = "provider_id", nullable = false)
    private UUID providerId;
    
    @Column(name = "provider_name", nullable = false, length = 100)
    private String providerName;
    
    @Column(name = "start_point_id", nullable = false)
    private UUID startPointId;
    
    @Column(name = "end_point_id", nullable = false)
    private UUID endPointId;
    
    @Column(nullable = false)
    private Integer capacity;
    
    @Column(name = "used_capacity")
    private Integer usedCapacity = 0;
    
    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal length;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Status status;
    
    @Column(name = "installation_date")
    private LocalDate installationDate;
    
    @UpdateTimestamp
    @Column(name = "last_modified")
    private LocalDateTime lastModified;
    
    @Column(name = "geometry_coordinates", nullable = false, columnDefinition = "TEXT")
    private String geometryCoordinates; // JSON string of coordinates array
    
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> properties;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
```

## API Endpoints

### Authentication Endpoints
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user info
- `POST /api/auth/refresh` - Refresh JWT token

### User Management Endpoints
- `GET /api/users` - Get all users (admin only)
- `GET /api/users/{id}` - Get user by ID
- `PUT /api/users/{id}` - Update user
- `DELETE /api/users/{id}` - Delete user (admin only)

### Connection Points Endpoints
- `GET /api/points` - Get all connection points (with optional provider filter)
- `GET /api/points/{id}` - Get connection point by ID
- `POST /api/points` - Create new connection point
- `PUT /api/points/{id}` - Update connection point
- `DELETE /api/points/{id}` - Delete connection point

### Optical Lines Endpoints
- `GET /api/lines` - Get all optical lines (with optional provider filter)
- `GET /api/lines/{id}` - Get optical line by ID
- `POST /api/lines` - Create new optical line
- `PUT /api/lines/{id}` - Update optical line
- `DELETE /api/lines/{id}` - Delete optical line

## Security Requirements

### Authentication
- JWT-based authentication
- Token expiration: 24 hours
- Refresh token mechanism
- Password hashing using BCrypt

### Authorization
- Role-based access control (RBAC)
- **Admin**: Full access to all resources
- **Provider**: Access to own resources only
- **Viewer**: Read-only access to all resources

### Security Headers
- CORS configuration for frontend domain
- CSRF protection
- Security headers (X-Frame-Options, X-Content-Type-Options, etc.)

## Validation Rules

### User Registration
- Username: 3-50 characters, alphanumeric and underscore only
- Email: Valid email format
- Password: Minimum 8 characters, at least one uppercase, lowercase, and number
- Provider name: Required for provider role

### Connection Points
- Name: Required, 1-100 characters
- Capacity: Positive integer
- Latitude: -90 to 90
- Longitude: -180 to 180
- Type: Must be one of: junction, endpoint, distribution
- Status: Must be one of: active, planned, maintenance, inactive

### Optical Lines
- Name: Required, 1-100 characters
- Capacity: Positive integer
- Used capacity: Non-negative, cannot exceed capacity
- Length: Positive decimal
- Start and end points: Must exist and belong to same provider
- Geometry coordinates: Valid JSON array of [longitude, latitude] pairs

## Error Handling

### Standard HTTP Status Codes
- 200: Success
- 201: Created
- 400: Bad Request (validation errors)
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 409: Conflict (duplicate resources)
- 500: Internal Server Error

### Error Response Format
```json
{
    "timestamp": "2024-01-15T10:30:00Z",
    "status": 400,
    "error": "Bad Request",
    "message": "Validation failed",
    "path": "/api/lines",
    "details": [
        {
            "field": "capacity",
            "message": "Capacity must be positive"
        }
    ]
}
```

## Performance Requirements

### Database Optimization
- Use database indexes for frequently queried fields
- Implement pagination for list endpoints (default: 20 items per page)
- Use database connection pooling
- Implement query optimization for geographic data

### Caching Strategy
- Cache user authentication data
- Cache frequently accessed reference data
- Implement Redis for session management (optional)

### API Performance
- Response time: < 200ms for simple queries
- Response time: < 1s for complex geographic queries
- Support for filtering, sorting, and pagination
- Implement rate limiting for API endpoints

## Testing Requirements

### Unit Tests
- Service layer tests with mocked dependencies
- Repository tests with @DataJpaTest
- Controller tests with @WebMvcTest
- Security configuration tests

### Integration Tests
- End-to-end API tests
- Database integration tests
- Authentication flow tests
- Authorization tests for different roles

### Test Data
- Create test fixtures for all entities
- Use TestContainers for database integration tests
- Mock external dependencies

## Deployment Considerations

### Environment Configuration
- Separate configurations for dev, test, and production
- Environment variables for sensitive data
- Database connection configuration
- JWT secret configuration

### Docker Support
- Dockerfile for application
- Docker Compose for local development
- Health check endpoints

### Monitoring and Logging
- Structured logging with JSON format
- Application metrics with Micrometer
- Health check endpoint: `/actuator/health`
- Info endpoint: `/actuator/info`

## Geographic Data Handling

Since PostGIS extensions should be avoided, implement geographic functionality using:

### Coordinate Storage
- Store latitude and longitude as separate DECIMAL columns
- Store line geometry as JSON string containing coordinate arrays
- Format: `[[lng1, lat1], [lng2, lat2], ...]`

### Geographic Calculations
- Implement distance calculations using Haversine formula
- Create utility classes for coordinate transformations
- Support for bounding box queries using latitude/longitude ranges

### GeoJSON Support
- Convert database coordinates to GeoJSON format in API responses
- Accept GeoJSON input and convert to coordinate storage format
- Support for Point and LineString geometries

## Additional Features

### Data Export
- Export endpoints for CSV and JSON formats
- Implement streaming for large datasets
- Support for filtered exports

### Audit Logging
- Track all CRUD operations
- Store user information for each change
- Implement soft delete for important entities

### Bulk Operations
- Bulk import endpoints for connection points and lines
- Validation for bulk data
- Transaction management for bulk operations

## Sample Implementation Examples

### Spring Boot Application Configuration
```java
@SpringBootApplication
@EnableJpaRepositories
@EnableScheduling
public class OpticalLinesDatabaseApplication {
    public static void main(String[] args) {
        SpringApplication.run(OpticalLinesDatabaseApplication.class, args);
    }
}
```

### Security Configuration
```java
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
public class SecurityConfig {

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint() {
        return new JwtAuthenticationEntryPoint();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf(csrf -> csrf.disable())
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .authorizeHttpRequests(auth -> auth
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/api/lines/**", "/api/points/**").hasAnyRole("ADMIN", "PROVIDER", "VIEWER")
                .requestMatchers(HttpMethod.POST, "/api/lines/**", "/api/points/**").hasAnyRole("ADMIN", "PROVIDER")
                .requestMatchers(HttpMethod.PUT, "/api/lines/**", "/api/points/**").hasAnyRole("ADMIN", "PROVIDER")
                .requestMatchers(HttpMethod.DELETE, "/api/lines/**", "/api/points/**").hasRole("ADMIN")
                .requestMatchers("/api/users/**").hasRole("ADMIN")
                .anyRequest().authenticated()
            )
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .addFilterBefore(jwtAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
}
```

### Repository Interfaces
```java
@Repository
public interface OpticalLineRepository extends JpaRepository<OpticalLine, UUID> {
    List<OpticalLine> findByProviderId(UUID providerId);
    List<OpticalLine> findByStatus(Status status);

    @Query("SELECT ol FROM OpticalLine ol WHERE ol.providerId = :providerId AND ol.status = :status")
    List<OpticalLine> findByProviderIdAndStatus(@Param("providerId") UUID providerId, @Param("status") Status status);

    @Query(value = "SELECT * FROM optical_lines WHERE " +
           "ST_DWithin(ST_MakePoint(CAST(JSON_EXTRACT(geometry_coordinates, '$[0][0]') AS DECIMAL), " +
           "CAST(JSON_EXTRACT(geometry_coordinates, '$[0][1]') AS DECIMAL)), " +
           "ST_MakePoint(:longitude, :latitude), :radiusKm * 1000)", nativeQuery = true)
    List<OpticalLine> findLinesNearPoint(@Param("latitude") double latitude,
                                       @Param("longitude") double longitude,
                                       @Param("radiusKm") double radiusKm);
}

@Repository
public interface ConnectionPointRepository extends JpaRepository<ConnectionPoint, UUID> {
    List<ConnectionPoint> findByProviderId(UUID providerId);
    List<ConnectionPoint> findByType(PointType type);
    List<ConnectionPoint> findByStatus(Status status);

    @Query("SELECT cp FROM ConnectionPoint cp WHERE " +
           "cp.latitude BETWEEN :minLat AND :maxLat AND " +
           "cp.longitude BETWEEN :minLng AND :maxLng")
    List<ConnectionPoint> findPointsInBoundingBox(@Param("minLat") BigDecimal minLat,
                                                @Param("maxLat") BigDecimal maxLat,
                                                @Param("minLng") BigDecimal minLng,
                                                @Param("maxLng") BigDecimal maxLng);
}
```

### Service Layer Example
```java
@Service
@Transactional
public class OpticalLineService {

    private final OpticalLineRepository lineRepository;
    private final ConnectionPointRepository pointRepository;
    private final OpticalLineMapper lineMapper;

    public OpticalLineService(OpticalLineRepository lineRepository,
                            ConnectionPointRepository pointRepository,
                            OpticalLineMapper lineMapper) {
        this.lineRepository = lineRepository;
        this.pointRepository = pointRepository;
        this.lineMapper = lineMapper;
    }

    @Transactional(readOnly = true)
    public List<OpticalLineDto> getAllLines(UUID providerId) {
        List<OpticalLine> lines = providerId != null
            ? lineRepository.findByProviderId(providerId)
            : lineRepository.findAll();
        return lineMapper.toDto(lines);
    }

    @Transactional(readOnly = true)
    public OpticalLineDto getLineById(UUID id) {
        OpticalLine line = lineRepository.findById(id)
            .orElseThrow(() -> new EntityNotFoundException("Optical line not found with id: " + id));
        return lineMapper.toDto(line);
    }

    public OpticalLineDto createLine(CreateOpticalLineDto createDto, UUID currentUserId) {
        // Validate that start and end points exist and belong to the same provider
        ConnectionPoint startPoint = pointRepository.findById(createDto.getStartPointId())
            .orElseThrow(() -> new EntityNotFoundException("Start point not found"));
        ConnectionPoint endPoint = pointRepository.findById(createDto.getEndPointId())
            .orElseThrow(() -> new EntityNotFoundException("End point not found"));

        if (!startPoint.getProviderId().equals(currentUserId) ||
            !endPoint.getProviderId().equals(currentUserId)) {
            throw new AccessDeniedException("Cannot create line between points of different providers");
        }

        OpticalLine line = lineMapper.toEntity(createDto);
        line.setProviderId(currentUserId);
        line.setProviderName(startPoint.getProviderName());

        // Calculate line length based on coordinates
        double calculatedLength = calculateLineLength(createDto.getGeometryCoordinates());
        line.setLength(BigDecimal.valueOf(calculatedLength));

        OpticalLine savedLine = lineRepository.save(line);
        return lineMapper.toDto(savedLine);
    }

    private double calculateLineLength(String geometryCoordinates) {
        // Implementation of Haversine formula for distance calculation
        // Parse JSON coordinates and calculate total distance
        // This is a simplified version - implement full calculation
        return 0.0;
    }
}
```

### Controller Example
```java
@RestController
@RequestMapping("/api/lines")
@Validated
public class OpticalLineController {

    private final OpticalLineService lineService;

    public OpticalLineController(OpticalLineService lineService) {
        this.lineService = lineService;
    }

    @GetMapping
    public ResponseEntity<List<OpticalLineDto>> getAllLines(
            @RequestParam(required = false) UUID providerId,
            Authentication authentication) {

        // If user is a provider, filter by their ID
        if (hasRole(authentication, "PROVIDER")) {
            UUID currentUserId = getCurrentUserId(authentication);
            providerId = currentUserId;
        }

        List<OpticalLineDto> lines = lineService.getAllLines(providerId);
        return ResponseEntity.ok(lines);
    }

    @GetMapping("/{id}")
    public ResponseEntity<OpticalLineDto> getLineById(@PathVariable UUID id) {
        OpticalLineDto line = lineService.getLineById(id);
        return ResponseEntity.ok(line);
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('PROVIDER')")
    public ResponseEntity<OpticalLineDto> createLine(
            @Valid @RequestBody CreateOpticalLineDto createDto,
            Authentication authentication) {

        UUID currentUserId = getCurrentUserId(authentication);
        OpticalLineDto createdLine = lineService.createLine(createDto, currentUserId);

        return ResponseEntity.status(HttpStatus.CREATED).body(createdLine);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or (hasRole('PROVIDER') and @opticalLineService.isOwner(#id, authentication.name))")
    public ResponseEntity<OpticalLineDto> updateLine(
            @PathVariable UUID id,
            @Valid @RequestBody UpdateOpticalLineDto updateDto,
            Authentication authentication) {

        OpticalLineDto updatedLine = lineService.updateLine(id, updateDto);
        return ResponseEntity.ok(updatedLine);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteLine(@PathVariable UUID id) {
        lineService.deleteLine(id);
        return ResponseEntity.noContent().build();
    }
}
```

### DTO Classes
```java
public class OpticalLineDto {
    private UUID id;
    private String name;
    private UUID providerId;
    private String providerName;
    private UUID startPointId;
    private UUID endPointId;
    private Integer capacity;
    private Integer usedCapacity;
    private BigDecimal length;
    private Status status;
    private LocalDate installationDate;
    private LocalDateTime lastModified;
    private GeoJsonLineString geometry; // Custom class for GeoJSON representation
    private Map<String, Object> properties;

    // Getters and setters
}

public class CreateOpticalLineDto {
    @NotBlank(message = "Name is required")
    @Size(max = 100, message = "Name must not exceed 100 characters")
    private String name;

    @NotNull(message = "Start point ID is required")
    private UUID startPointId;

    @NotNull(message = "End point ID is required")
    private UUID endPointId;

    @NotNull(message = "Capacity is required")
    @Positive(message = "Capacity must be positive")
    private Integer capacity;

    @Min(value = 0, message = "Used capacity cannot be negative")
    private Integer usedCapacity = 0;

    @NotNull(message = "Status is required")
    private Status status;

    private LocalDate installationDate;

    @NotBlank(message = "Geometry coordinates are required")
    private String geometryCoordinates;

    private Map<String, Object> properties;

    // Getters and setters
}
```

### Application Properties
```properties
# Database Configuration
spring.datasource.url=*************************************************
spring.datasource.username=${DB_USERNAME:optical_user}
spring.datasource.password=${DB_PASSWORD:optical_password}
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true

# Flyway Configuration
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true

# JWT Configuration
app.jwt.secret=${JWT_SECRET:mySecretKey}
app.jwt.expiration=86400000
app.jwt.refresh-expiration=604800000

# CORS Configuration
app.cors.allowed-origins=http://localhost:4200
app.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
app.cors.allowed-headers=*
app.cors.allow-credentials=true

# Server Configuration
server.port=8080
server.servlet.context-path=/

# Logging Configuration
logging.level.com.opticallines=DEBUG
logging.level.org.springframework.security=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized
```

## Provider Dashboard & TODO Features

### Provider Dashboard API Endpoints

#### Dashboard Statistics
- `GET /api/provider/dashboard/stats` - Get provider overview statistics
  ```json
  {
    "totalLines": 8,
    "totalPoints": 8,
    "totalCapacity": 5700,
    "usedCapacity": 4200,
    "utilizationPercentage": 73.7,
    "activeLines": 7,
    "plannedLines": 0,
    "maintenanceLines": 1,
    "inactiveLines": 0,
    "recentActivity": [
      {
        "type": "line_created",
        "description": "New line Bratislava-Trnava created",
        "timestamp": "2024-01-15T10:30:00Z"
      }
    ]
  }
  ```

#### Capacity Management
- `GET /api/provider/capacity/overview` - Get capacity utilization overview
- `GET /api/provider/capacity/lines` - Get lines with capacity details
- `PUT /api/provider/capacity/lines/{id}` - Update line capacity utilization
- `GET /api/provider/capacity/alerts` - Get capacity threshold alerts

#### Provider-specific Line Management
- `GET /api/provider/lines` - Get provider's lines only
- `POST /api/provider/lines` - Create new line (auto-assigns to current provider)
- `PUT /api/provider/lines/{id}` - Update provider's line
- `GET /api/provider/lines/{id}/utilization` - Get line utilization history

#### Provider-specific Point Management
- `GET /api/provider/points` - Get provider's connection points only
- `POST /api/provider/points` - Create new connection point
- `PUT /api/provider/points/{id}` - Update provider's connection point
- `GET /api/provider/points/{id}/connections` - Get connected lines for a point

### Data Export Features

#### Export Endpoints
- `GET /api/export/lines?format=csv&providerId={id}` - Export lines as CSV
- `GET /api/export/lines?format=json&providerId={id}` - Export lines as JSON
- `GET /api/export/points?format=csv&providerId={id}` - Export points as CSV
- `GET /api/export/points?format=json&providerId={id}` - Export points as JSON
- `GET /api/export/report?providerId={id}&format=pdf` - Generate provider report

#### Bulk Import Features
- `POST /api/import/lines` - Bulk import lines from CSV/JSON
- `POST /api/import/points` - Bulk import connection points
- `GET /api/import/template/lines` - Download CSV template for lines
- `GET /api/import/template/points` - Download CSV template for points

### Search and Filtering Features

#### Advanced Search
- `GET /api/search/lines?q={query}&filters={filters}` - Search lines with filters
- `GET /api/search/points?q={query}&filters={filters}` - Search points with filters
- `GET /api/search/global?q={query}` - Global search across all entities

#### Geographic Queries
- `GET /api/geo/lines/nearby?lat={lat}&lng={lng}&radius={km}` - Find lines near location
- `GET /api/geo/points/nearby?lat={lat}&lng={lng}&radius={km}` - Find points near location
- `GET /api/geo/lines/bbox?minLat={}&maxLat={}&minLng={}&maxLng={}` - Lines in bounding box
- `GET /api/geo/points/bbox?minLat={}&maxLat={}&minLng={}&maxLng={}` - Points in bounding box

### Notification System

#### Notification Endpoints
- `GET /api/notifications` - Get user notifications
- `POST /api/notifications/mark-read/{id}` - Mark notification as read
- `GET /api/notifications/unread-count` - Get unread notification count
- `POST /api/notifications/settings` - Update notification preferences

#### Notification Types
- Capacity threshold alerts (>80% utilization)
- Line status changes (maintenance, inactive)
- New line/point approvals (if workflow implemented)
- System maintenance notifications

### Analytics and Reporting

#### Analytics Endpoints
- `GET /api/analytics/capacity-trends?period={days}` - Capacity utilization trends
- `GET /api/analytics/network-growth?period={days}` - Network growth statistics
- `GET /api/analytics/provider-comparison` - Provider comparison metrics
- `GET /api/analytics/geographic-distribution` - Geographic distribution of infrastructure

#### Report Generation
- `GET /api/reports/provider/{id}/monthly` - Monthly provider report
- `GET /api/reports/network/status` - Network status report
- `GET /api/reports/capacity/utilization` - Capacity utilization report
- `GET /api/reports/maintenance/schedule` - Maintenance schedule report

### User Management & Profile Features

#### User Profile Management
- `GET /api/profile` - Get current user profile
- `PUT /api/profile` - Update user profile
- `POST /api/profile/change-password` - Change user password
- `GET /api/profile/activity` - Get user activity history

#### Admin User Management
- `GET /api/admin/users` - Get all users (admin only)
- `POST /api/admin/users` - Create new user (admin only)
- `PUT /api/admin/users/{id}` - Update user (admin only)
- `DELETE /api/admin/users/{id}` - Deactivate user (admin only)
- `POST /api/admin/users/{id}/reset-password` - Reset user password

### Audit and Logging Features

#### Audit Trail
- `GET /api/audit/lines/{id}` - Get line change history
- `GET /api/audit/points/{id}` - Get point change history
- `GET /api/audit/user-actions?userId={id}` - Get user action history

#### System Logs (Admin only)
- `GET /api/admin/logs/api-access` - API access logs
- `GET /api/admin/logs/errors` - System error logs
- `GET /api/admin/logs/security` - Security event logs

### Validation and Business Rules

#### Line Creation Validation
- Start and end points must exist
- Start and end points must belong to the same provider (for provider role)
- Capacity must be positive
- Used capacity cannot exceed total capacity
- Geometry coordinates must be valid JSON array

#### Point Creation Validation
- Coordinates must be within valid ranges (lat: -90 to 90, lng: -180 to 180)
- Name must be unique within provider
- Capacity must be positive
- Type must be valid enum value

#### Provider-specific Rules
- Providers can only manage their own lines and points
- Providers cannot create lines between points of different providers
- Admin can manage all resources
- Viewer has read-only access to all resources

### Database Migration Scripts

#### V1__Create_initial_schema.sql
```sql
-- Create users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'provider', 'viewer')),
    provider_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Create connection_points table
CREATE TABLE connection_points (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    provider_id UUID NOT NULL REFERENCES users(id),
    provider_name VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('junction', 'endpoint', 'distribution')),
    capacity INTEGER NOT NULL,
    address TEXT,
    status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'planned', 'maintenance', 'inactive')),
    installation_date DATE,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    properties JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create optical_lines table
CREATE TABLE optical_lines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    provider_id UUID NOT NULL REFERENCES users(id),
    provider_name VARCHAR(100) NOT NULL,
    start_point_id UUID NOT NULL REFERENCES connection_points(id),
    end_point_id UUID NOT NULL REFERENCES connection_points(id),
    capacity INTEGER NOT NULL,
    used_capacity INTEGER DEFAULT 0,
    length DECIMAL(10, 2) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'planned', 'maintenance', 'inactive')),
    installation_date DATE,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    geometry_coordinates TEXT NOT NULL,
    properties JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_connection_points_provider ON connection_points(provider_id);
CREATE INDEX idx_connection_points_status ON connection_points(status);
CREATE INDEX idx_connection_points_location ON connection_points(latitude, longitude);
CREATE INDEX idx_optical_lines_provider ON optical_lines(provider_id);
CREATE INDEX idx_optical_lines_status ON optical_lines(status);
CREATE INDEX idx_optical_lines_points ON optical_lines(start_point_id, end_point_id);
```

#### V2__Insert_sample_data.sql
```sql
-- Insert sample users (passwords are all 'password123')
INSERT INTO users (id, username, email, password_hash, role, provider_name, created_at, is_active) VALUES
('550e8400-e29b-41d4-a716-446655440000', 'admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lQOjkNnL.Ga', 'admin', NULL, CURRENT_TIMESTAMP, true),
('550e8400-e29b-41d4-a716-446655440001', 'provider', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lQOjkNnL.Ga', 'provider', 'Slovak Telekom', CURRENT_TIMESTAMP, true),
('550e8400-e29b-41d4-a716-446655440002', 'provider2', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lQOjkNnL.Ga', 'provider', 'Orange Slovakia', CURRENT_TIMESTAMP, true),
('550e8400-e29b-41d4-a716-446655440003', 'viewer', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lQOjkNnL.Ga', 'viewer', NULL, CURRENT_TIMESTAMP, true);

-- Insert sample connection points (matching frontend mock data)
INSERT INTO connection_points (id, name, provider_id, provider_name, type, capacity, address, status, installation_date, latitude, longitude, properties) VALUES
-- Slovak Telekom points
('point1', 'Bratislava Central Hub', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'junction', 10000, 'Bratislava, Slovakia', 'active', '2020-01-01', 48.148, 17.107, '{}'),
('point2', 'Trnava Distribution', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'distribution', 5000, 'Trnava, Slovakia', 'active', '2020-02-01', 48.377, 17.583, '{}'),
('point3', 'Nitra Junction', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'junction', 8000, 'Nitra, Slovakia', 'active', '2020-03-01', 48.308, 18.087, '{}'),
('point4', 'Trenčín Endpoint', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'endpoint', 3000, 'Trenčín, Slovakia', 'active', '2020-04-01', 48.894, 18.044, '{}'),
('point5', 'Žilina Hub', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'junction', 7000, 'Žilina, Slovakia', 'active', '2020-05-01', 49.223, 18.740, '{}'),
('point6', 'Košice Central', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'junction', 9000, 'Košice, Slovakia', 'active', '2020-06-01', 48.717, 21.261, '{}'),
('point7', 'Prešov Distribution', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'distribution', 4000, 'Prešov, Slovakia', 'active', '2020-07-01', 49.002, 21.239, '{}'),
('point8', 'Banská Bystrica Hub', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'junction', 6000, 'Banská Bystrica, Slovakia', 'active', '2020-08-01', 48.736, 19.146, '{}'),
-- Orange Slovakia points
('point9', 'Orange Bratislava', '550e8400-e29b-41d4-a716-446655440002', 'Orange Slovakia', 'junction', 8000, 'Bratislava, Slovakia', 'active', '2021-01-01', 48.158, 17.117, '{}'),
('point10', 'Orange Košice', '550e8400-e29b-41d4-a716-446655440002', 'Orange Slovakia', 'junction', 7000, 'Košice, Slovakia', 'active', '2021-02-01', 48.727, 21.271, '{}'),
('point11', 'Orange Žilina', '550e8400-e29b-41d4-a716-446655440002', 'Orange Slovakia', 'distribution', 5000, 'Žilina, Slovakia', 'active', '2021-03-01', 49.233, 18.750, '{}'),
('point12', 'Orange Nitra', '550e8400-e29b-41d4-a716-446655440002', 'Orange Slovakia', 'endpoint', 3000, 'Nitra, Slovakia', 'planned', '2024-01-01', 48.318, 18.097, '{}');

-- Insert sample optical lines (matching frontend mock data)
INSERT INTO optical_lines (id, name, provider_id, provider_name, start_point_id, end_point_id, capacity, used_capacity, length, status, installation_date, geometry_coordinates, properties) VALUES
-- Slovak Telekom lines
('1', 'Bratislava-Trnava', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'point1', 'point2', 1000, 750, 47.5, 'active', '2020-01-15', '[[17.107, 48.148], [17.583, 48.377]]', '{}'),
('2', 'Trnava-Nitra', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'point2', 'point3', 800, 600, 65.2, 'active', '2020-02-20', '[[17.583, 48.377], [18.087, 48.308]]', '{}'),
('3', 'Bratislava-Trenčín', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'point1', 'point4', 600, 450, 120.8, 'active', '2020-03-10', '[[17.107, 48.148], [18.044, 48.894]]', '{}'),
('4', 'Trenčín-Žilina', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'point4', 'point5', 500, 300, 85.3, 'active', '2020-04-05', '[[18.044, 48.894], [18.740, 49.223]]', '{}'),
('5', 'Žilina-Košice', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'point5', 'point6', 1200, 900, 280.5, 'active', '2020-05-12', '[[18.740, 49.223], [21.261, 48.717]]', '{}'),
('6', 'Košice-Prešov', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'point6', 'point7', 400, 250, 35.7, 'active', '2020-06-18', '[[21.261, 48.717], [21.239, 49.002]]', '{}'),
('7', 'Nitra-Banská Bystrica', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'point3', 'point8', 700, 500, 95.4, 'active', '2020-07-22', '[[18.087, 48.308], [19.146, 48.736]]', '{}'),
('8', 'Bratislava-Banská Bystrica', '550e8400-e29b-41d4-a716-446655440001', 'Slovak Telekom', 'point1', 'point8', 900, 650, 180.2, 'maintenance', '2020-08-30', '[[17.107, 48.148], [19.146, 48.736]]', '{}'),
-- Orange Slovakia lines
('9', 'Orange Bratislava-Košice', '550e8400-e29b-41d4-a716-446655440002', 'Orange Slovakia', 'point9', 'point10', 1500, 1100, 350.8, 'active', '2021-01-20', '[[17.117, 48.158], [21.271, 48.727]]', '{}'),
('10', 'Orange Košice-Žilina', '550e8400-e29b-41d4-a716-446655440002', 'Orange Slovakia', 'point10', 'point11', 800, 600, 220.3, 'active', '2021-02-25', '[[21.271, 48.727], [18.750, 49.233]]', '{}'),
('11', 'Orange Žilina-Nitra', '550e8400-e29b-41d4-a716-446655440002', 'Orange Slovakia', 'point11', 'point12', 600, 0, 140.7, 'planned', '2024-03-01', '[[18.750, 49.233], [18.097, 48.318]]', '{}');
```

## Essential Configuration

### Application Properties
```properties
# Database Configuration
spring.datasource.url=*************************************************
spring.datasource.username=${DB_USERNAME:optical_user}
spring.datasource.password=${DB_PASSWORD:optical_password}

# JPA Configuration
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false

# JWT Configuration
app.jwt.secret=${JWT_SECRET:mySecretKey}
app.jwt.expiration=86400000

# CORS Configuration
app.cors.allowed-origins=http://localhost:4200

# Server Configuration
server.port=8080
```

### Required Dependencies
- Spring Boot 3.x (Web, Data JPA, Security, Validation)
- PostgreSQL driver
- JWT library (jjwt)
- Flyway for migrations
- MapStruct for mapping
- OpenAPI for documentation

### API Documentation
- Swagger UI: `http://localhost:8080/swagger-ui.html`
- Health check: `GET /actuator/health`

## Frontend Integration Points

### Authentication Flow
1. Frontend sends login request to `/api/auth/login`
2. Backend validates credentials and returns JWT token
3. Frontend stores token and includes in Authorization header
4. Backend validates token on each request using interceptor

### Data Synchronization
- Frontend expects exact same data structure as defined in TypeScript interfaces
- Geographic data should be returned as GeoJSON format for map display
- Pagination should match frontend table component expectations
- Error responses should follow consistent format for frontend error handling

### Real-time Features (Future)
- WebSocket endpoints for real-time capacity updates
- Server-sent events for notifications
- Live map updates when data changes

This comprehensive specification provides all the necessary details for developing a robust Java backend that will seamlessly integrate with the existing Angular frontend application.
