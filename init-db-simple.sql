-- PostgreSQL initialization script for BMS-BE
-- This script assumes user 'dbuser' with password 'db<PERSON><PERSON>' already exists
-- Run this script as a PostgreSQL superuser (e.g., postgres)

-- Create database (skip if it already exists)
CREATE DATABASE bms_db;

-- Connect to the database
\connect bms_db

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE bms_db TO dbuser;
GRANT ALL ON SCHEMA public TO dbuser;

-- PostGIS extension removed - using simple coordinates instead

-- Create enum types
CREATE TYPE connection_point_type AS ENUM ('junction', 'endpoint', 'distribution');
CREATE TYPE connection_point_status AS ENUM ('active', 'planned', 'maintenance', 'inactive');
CREATE TYPE optical_line_status AS ENUM ('active', 'planned', 'maintenance', 'inactive');
CREATE TYPE user_role AS ENUM ('admin', 'provider', 'viewer');

-- Create providers table
CREATE TABLE providers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role user_role NOT NULL,
    provider_id UUID REFERENCES providers(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    active BOOLEAN DEFAULT TRUE
);

-- Create connection_points table
CREATE TABLE connection_points (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    provider_id UUID REFERENCES providers(id) ON DELETE CASCADE,
    type connection_point_type NOT NULL,
    capacity INTEGER NOT NULL DEFAULT 0,
    address VARCHAR(255),
    status connection_point_status NOT NULL DEFAULT 'planned',
    installation_date TIMESTAMP,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    properties JSONB
);

-- Create optical_lines table
CREATE TABLE optical_lines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    provider_id UUID REFERENCES providers(id) ON DELETE CASCADE,
    capacity INTEGER NOT NULL DEFAULT 0,
    utilization INTEGER NOT NULL DEFAULT 0,
    status optical_line_status NOT NULL DEFAULT 'planned',
    installation_date TIMESTAMP,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    path_coordinates JSONB, -- Array of coordinate pairs: [{"lat": 48.1486, "lng": 17.1077}, ...]
    properties JSONB
);

-- Create connection_point_line table for many-to-many relationship
CREATE TABLE connection_point_line (
    connection_point_id UUID REFERENCES connection_points(id) ON DELETE CASCADE,
    optical_line_id UUID REFERENCES optical_lines(id) ON DELETE CASCADE,
    is_source BOOLEAN NOT NULL DEFAULT FALSE,
    PRIMARY KEY (connection_point_id, optical_line_id)
);

-- Create refresh_tokens table
CREATE TABLE refresh_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(255) NOT NULL UNIQUE,
    expiry_date TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_connection_points_provider ON connection_points(provider_id);
CREATE INDEX idx_optical_lines_provider ON optical_lines(provider_id);
CREATE INDEX idx_users_provider ON users(provider_id);
CREATE INDEX idx_connection_points_coordinates ON connection_points(latitude, longitude);
CREATE INDEX idx_optical_lines_path_coordinates ON optical_lines USING GIN(path_coordinates);

-- Create triggers for last_modified
CREATE OR REPLACE FUNCTION update_last_modified()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_modified = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_connection_points_last_modified
BEFORE UPDATE ON connection_points
FOR EACH ROW EXECUTE FUNCTION update_last_modified();

CREATE TRIGGER update_optical_lines_last_modified
BEFORE UPDATE ON optical_lines
FOR EACH ROW EXECUTE FUNCTION update_last_modified();

CREATE TRIGGER update_providers_last_modified
BEFORE UPDATE ON providers
FOR EACH ROW EXECUTE FUNCTION update_last_modified();

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, password_hash, role)
VALUES ('admin', '<EMAIL>', '$2a$10$dXJ3SW6G7P50lGmMkkmwe.20cQQubK3.HZWzG3YB1tlRy.fqvM/BG', 'admin');

-- Insert sample providers
INSERT INTO providers (id, name, contact_email, contact_phone, active)
VALUES
  ('11111111-1111-1111-1111-111111111111', 'Telekom', '<EMAIL>', '+421900123456', true),
  ('22222222-2222-2222-2222-222222222222', 'Orange', '<EMAIL>', '+421901234567', true),
  ('33333333-3333-3333-3333-333333333333', 'O2', '<EMAIL>', '+421902345678', true);

-- Insert sample connection points
INSERT INTO connection_points (id, name, provider_id, type, capacity, address, status, latitude, longitude)
VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Junction A', '11111111-1111-1111-1111-111111111111', 'junction', 100, 'Main Street 1, Bratislava', 'active', 48.1486, 17.1077),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Endpoint B', '22222222-2222-2222-2222-222222222222', 'endpoint', 50, 'Secondary Street 2, Košice', 'active', 48.7164, 21.2611),
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'Distribution C', '33333333-3333-3333-3333-333333333333', 'distribution', 200, 'Third Street 3, Banská Bystrica', 'active', 48.7395, 19.1535);

-- Insert sample optical lines
INSERT INTO optical_lines (id, name, provider_id, capacity, utilization, status, path_coordinates)
VALUES
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', 'Line 1', '11111111-1111-1111-1111-111111111111', 100, 30, 'active', '[{"lat": 48.1486, "lng": 17.1077}, {"lat": 48.7395, "lng": 19.1535}]'::jsonb),
  ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'Line 2', '22222222-2222-2222-2222-222222222222', 50, 20, 'active', '[{"lat": 48.7395, "lng": 19.1535}, {"lat": 48.7164, "lng": 21.2611}]'::jsonb);

-- Connect connection points to optical lines
INSERT INTO connection_point_line (connection_point_id, optical_line_id, is_source)
VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'dddddddd-dddd-dddd-dddd-dddddddddddd', true),
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'dddddddd-dddd-dddd-dddd-dddddddddddd', false),
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', true),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', false);

-- Grant all privileges on all tables to dbuser
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO dbuser;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO dbuser;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO dbuser;

-- Create a Flyway schema history table to prevent Flyway from trying to run migrations
-- This is only needed if you're using this script instead of letting Flyway handle migrations
CREATE TABLE IF NOT EXISTS flyway_schema_history (
    installed_rank INTEGER NOT NULL,
    version VARCHAR(50),
    description VARCHAR(200) NOT NULL,
    type VARCHAR(20) NOT NULL,
    script VARCHAR(1000) NOT NULL,
    checksum INTEGER,
    installed_by VARCHAR(100) NOT NULL,
    installed_on TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    execution_time INTEGER NOT NULL,
    success BOOLEAN NOT NULL,
    PRIMARY KEY (installed_rank)
);

-- Insert records for our migrations to mark them as already applied
INSERT INTO flyway_schema_history (installed_rank, version, description, type, script, checksum, installed_by, installed_on, execution_time, success)
VALUES
  (1, '1', 'Initial Schema', 'SQL', 'V1__Initial_Schema.sql', 0, 'manual', CURRENT_TIMESTAMP, 0, true),
  (2, '2', 'Sample Data', 'SQL', 'V2__Sample_Data.sql', 0, 'manual', CURRENT_TIMESTAMP, 0, true);
